import os
import torch
import json
from PIL import Image
from tqdm import tqdm
from datasets import Dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
    CLIPImageProcessor,
)
from peft import get_peft_model, LoraConfig, TaskType
from transformers import DataCollatorForLanguageModeling



MODEL_NAME = "Qwen/QwenVL-Chat-Int4"
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

# 加载模型（4bit量化 + LoRA）
quant_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_use_double_quant=True,
    bnb_4bit_compute_dtype=torch.float16
)

model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    quantization_config=quant_config,
    device_map="auto"
)

# LoRA 插件
lora_config = LoraConfig.from_pretrained("lora_config.json") \
    if os.path.exists("lora_config.json") else LoraConfig(
    r=64,
    lora_alpha=16,
    target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    lora_dropout=0.05,
    task_type=TaskType.CAUSAL_LM
)

model = get_peft_model(model, lora_config)
model.print_trainable_parameters()

tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME, trust_remote_code=True)
tokenizer.pad_token = tokenizer.eos_token

image_processor = CLIPImageProcessor.from_pretrained(MODEL_NAME)

# 加载图文对数据
def load_data(json_file):
    with open(json_file, "r") as f:
        samples = json.load(f)

    def gen():
        for item in samples:
            image = Image.open(item["image"]).convert("RGB")
            image_tensor = image_processor(image, return_tensors="pt")["pixel_values"][0]
            yield {
                "image": image_tensor,
                "instruction": item["instruction"],
                "response": item["response"]
            }

    return Dataset.from_generator(gen)

train_dataset = load_data("data/train.json")
val_dataset = load_data("data/val.json")

# 编码函数（图文输入）
def tokenize_sample(example):
    prompt = f"<|user|>\n{example['instruction']}\n<|image|>\n<|assistant|>\n{example['response']}"
    tokenized = tokenizer(prompt, truncation=True, padding="max_length", max_length=1024)
    tokenized["labels"] = tokenized["input_ids"].copy()
    tokenized["pixel_values"] = example["image"]
    return tokenized

train_dataset = train_dataset.map(tokenize_sample)
val_dataset = val_dataset.map(tokenize_sample)

# 训练配置
training_args = TrainingArguments(
    output_dir="./qwenvl-damage-lora",
    per_device_train_batch_size=2,
    per_device_eval_batch_size=2,
    evaluation_strategy="epoch",
    save_strategy="epoch",
    num_train_epochs=3,
    logging_dir="./logs",
    logging_steps=10,
    learning_rate=2e-4,
    save_total_limit=2,
    fp16=True,
    report_to="none"  # 可改为 "wandb" 或 "swanlab"
)

trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    data_collator=DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)
)

trainer.train()
model.save_pretrained("./qwenvl-damage-lora")
tokenizer.save_pretrained("./qwenvl-damage-lora")
