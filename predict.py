from transformers import AutoTokenizer, AutoModelForCausalLM
from PIL import Image

tokenizer = AutoTokenizer.from_pretrained("./qwenvl-damage-lora", trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained("./qwenvl-damage-lora", trust_remote_code=True, device_map="auto")

image = Image.open("images/car_test.jpg").convert("RGB")
prompt = "请分析这辆汽车的侧面是否有损伤"

response = model.chat(tokenizer, query=prompt, image=image)
print(response)
